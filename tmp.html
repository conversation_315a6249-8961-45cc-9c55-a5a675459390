<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Continuous Single Text Marquee</title>
<style>
  body {
    font-family: Arial, sans-serif;
    padding: 20px;
  }

  .marquee-container {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    border: 1px solid #ddd;
    background: #f9f9f9;
    padding: 10px 0;
  }

  .marquee-content {
    display: inline-block;
    padding-left: 100%; /* start off-screen on the right */
    animation: scroll-left 10s linear infinite;
  }

  .marquee-content > span {
    padding-right: 30px; /* gap between loops */
  }

  /* Animate the text moving from 0% to -50% */
  @keyframes scroll-left {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }
</style>
</head>
<body>

<h2>Single Text Marquee Loop with 30px Overlap</h2>
<div class="marquee-container">
  <div class="marquee-content" aria-label="Scrolling marquee text">
    <span>This is the marquee text scrolling continuously</span>
    <span>This is the marquee text scrolling continuously</span>
  </div>
</div>

</body>
</html>
