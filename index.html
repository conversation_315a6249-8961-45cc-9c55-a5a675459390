<!DOCTYPE html>
<!--[if IE 8]> 
<html lang="en" class="ie8">
   <![endif]-->
<!--[if IE 9]> 
   <html lang="en" class="ie9">
      <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->

<head>
   <title>My Resume</title>
   <!-- Meta -->
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="Personal CV">
   <meta name="author" content="Rahimbayli Sattar">
   <link rel="shortcut icon" href="Lite-Icon-icon.png">
   <link
      href='https://fonts.googleapis.com/css?family=Roboto:400,500,400italic,300italic,300,500italic,700,700italic,900,900italic'
      rel='stylesheet' type='text/css'>
   <!-- Global CSS -->
   <link rel="stylesheet" href="assets/plugins/bootstrap/css/bootstrap.min.css">
   <link rel="stylesheet" href="assets/css/main-style.css">
   <!-- Plugins CSS -->
   <link rel="stylesheet" href="assets/plugins/font-awesome/css/font-awesome.css">
   <link id="theme-style" rel="stylesheet" href="assets/css/styles.css">
</head>

<body>
   <div class="wrapper">
      <div class="sidebar-wrapper">
         <div class="profile-container">
            <img class="profile" src="assets/images/circle.png" alt="" />
            <h1 class="name">Rahimbayli Sattar</h1>
            <h3 class="tagline">Software Developer</h3>
            <br />
             <a href="assets/files/CV.pdf" target="_blank">Download PDF</a>
         </div>
         <!--//profile-container-->
         <div class="contact-container container-block">
            <ul class="list-unstyled contact-list">
               <li class="email"><i class="fa fa-envelope"></i><a
                     href="mailto: <EMAIL>"><EMAIL></a></li>
               <li class="phone"><i class="fa fa-send"></i><a href="https://t.me/rsattar93">Sattar Rahimbayli</a></li>
               <li class="linkedin"><i class="fa fa-linkedin"></i><a
                     href="https://www.linkedin.com/in/sattar-rahimbeyli-953784b6" target="_blank">Sattar Rahimbayli</a>
               </li>
               <li class="github"><i class="fa fa-github"></i><a href="https://github.com/device999"
                     target="_blank">device999</a></li>               
               <li class="twitter"><i class="fa fa-android"></i><a href="https://play.google.com/store/apps/dev?id=8233666142431832771">Sattar Rahimbayli</a></li>
            </ul>
         </div>
         <!--//contact-container-->
         <div class="education-container container-block">
            <h2 class="container-block-title">Education</h2>
            <div class="item">
               <h4 class="degree">MSc in Computer Science</h4>
               <h5 class="meta">University of Bonn</h5>
               <div class="time">2014 - 2018</div>
            </div>
            <!--//item-->
            <div class="item">
               <h4 class="degree">BSc in Computer Science</h4>
               <h5 class="meta">Azerbaijan State Oil Academy</h5>
               <div class="time">2010 - 2014</div>
            </div>
            <!--//item-->
         </div>
         <!--//education-container-->
         <div class="languages-container container-block">
            <h2 class="container-block-title">Languages</h2>
            <ul class="list-unstyled interests-list">
               <li>Russian <span class="lang-desc">(Native)</span></li>
               <li>Azerbaijani <span class="lang-desc">(Native)</span></li>
               <li>Turkish <span class="lang-desc">(Professional)</span></li>
               <li>English <span class="lang-desc">(Professional)</span></li>
               <li>German <span class="lang-desc">(Intermediate)</span></li>
            </ul>
         </div>
         <!--//interests-->
         <div class="interests-container container-block">
            <h2 class="container-block-title">Interests</h2>
            <ul class="list-unstyled interests-list">
               <li>Medical Informatics</li>
               <li>Machine Learning</li>
            </ul>
         </div>
         <!--//interests-->
      </div>
      <!--//sidebar-wrapper-->
      <div class="main-wrapper">
         <!--//skills-section-->
         <section class="skills-section section">
            <h2 class="section-title"><i class="fa fa-rocket"></i>Skills</h2>
            <div>
               <p><span class="job-title">Backend:</span> Java, Javascript / Typescript, Python</p>
               <p><span class="job-title">Frameworks:</span> Quarkus, Spring Boot, Hibernate, React Native, 
                  Angular 6-8, Django, Play Framework, Angular JS, Express.js, GWT, CoffeeScript
               </p>
               <p><span class="job-title">Frontend:</span> React, Ajax, JQuery, Bootstrap 3, Materialize, Kendo, Scss,
                  Sass, Less</p>
               <p><span class="job-title">Testing:</span> JUnit, Mockito, Jest</p>
               <p><span class="job-title">Databases:</span> SQL, MySQL, PostgreSQL, Mongo, DB2, Firebase</p>
               <p><span class="job-title">CI/CD:</span> Docker, CircleCI, Jenkins, Bitbucket pipelines, Gitlab pipelines, Bamboo
               </p>
               <p><span class="job-title">Build tools:</span> Maven, SBT, ANT, Gradle</p>
               <!-- <p><span class="job-title">CMS:</span> Wordpress, Joomla, Kirby, Typo3</p> -->
            </div>
         </section>
         
         <!--//experiences-section-->
         <section class="section experiences-section">
            <h2 class="section-title"><i class="fa fa-briefcase"></i>Experiences</h2>
            
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Software Developer</h3>
                     <div class="time">April 2022 - now</div>
                  </div>
                  <div class="company">Dedalus HealthCare GmbH, Bonn</div>
               </div>
               <b>Working on different projects</b>
               <div class="details">
                  <table>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of an API based on Quarkus, and integration into the Camunda Engine using REST architecture, as part of a microservice architecture                          
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of an online front desk and waiting room for a hospital, using Spring Boot and Angular
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of a patient portal for a hospital, using Spring Boot and Angular
                        </td>
                     </tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           <i>
                              Technologies: Java, Quarkus, Spring Boot, Angular, CoffeeScript, Docker
                           </i>
                        </td>
                     </tr>
                  </table>
               </div>
            </div>
  

            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Software Developer</h3>
                     <div class="time">September 2020 - March 2022</div>
                  </div>
                  <!--//upper-row-->
                  <div class="company">Universitätsklinikum, Bonn</div>
               </div>
               <b>Worked in different projects</b>
               <div class="details">
                  <table>


                     <!--
                        End
                     -->
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of a visualization and search solution for FHIR bundles using Angular
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of additional modules to transform medical data into FHIR entities
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           <i>
                              Technologies: Java, Spring Boot, Angular, Docker
                           </i>
                        </td>
                     </tr>
                  </table>
                  <p><a href="assets/files/UKBRec.pdf" target="_blank">Recommendation Letter (in German)</a> </p>
               </div>
            </div>

            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Software Developer</h3>
                     <div class="time">April 2019 - August 2020</div>
                  </div>
                  <!--//upper-row-->
                  <div class="company">CodeFrog IT GmbH, Braunschweig</div>
               </div>
               <b>Working in different projects for different clients</b>
               <div class="details">
                  <table>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Creating financial mobile app for stock trading with the following tech stack: React
                           Native with Spring Boot backend and Bitbucket Pipelines with Docker for CI/CD
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Creating time management web app based on Angular with Spring Boot backend and
                           Jenkins with Docker for CI/CD
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Creating platform for management of vehicle delivery and sales based on Angular with
                           Spring boot backend and Bamboo for CI/CD
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           <i>
                              Technologies: Java, Typescript, Spring Boot, Angular, React Native, RxJS, Redux, Mongo
                              DB, MySQL, Docker, Jenkins, Bamboo, Bitbucket Pipelines, Jest, Mockito, SonarQube
                           </i>
                        </td>
                     </tr>
                  </table>
                  <p><a href="assets/files/codefrogRec.pdf" target="_blank">Recommendation Letter (in German)</a> </p>
               </div>
            </div>

            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Software Developer</h3>
                     <div class="time">June 2017 - March 2019</div>
                  </div>
                  <!--//upper-row-->
                  <div class="company">Peter L. Reichertz Institut for Medical Informatics, Braunschweig</div>
               </div>
               <div class="details">
                  <table>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of rule-based reward system with Play Framework
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Extending an existing Java-based back-end service for clinical trial management system
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Managing deployment server, together with database administration and ci/cd pipelines
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           <i>Technologies: Java, Javascript, Play Framework, Hibernate, GWT, MySQL, BitBucket
                              pipelines, Docker</i>
                        </td>
                     </tr>
                  </table>
               </div>
            </div>



            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Web-Developer</h3>
                     <div class="time">August 2016 - June 2017</div>
                  </div>
                  <!--//upper-row-->
                  <div class="company">Advanced Dynamics, Cologne</div>
               </div>
               <!--//meta-->
               <b>Worked on different projects</b>
               <div class="details">
                  <table>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of internal time management calendar based on Angular
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Building several media apps on Wordpress
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Development of web scrapping tools
                        </td>
                     </tr>
                     <tr>
                        <td> <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           On-going maintenance of apps with constant user flow
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Optimization of the application for maximum speed and scalability
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           <i>Technologies: PHP, Cake, Angular, MySQL, Wordpress</i>
                        </td>
                     </tr>
                  </table>
               </div>
               <!--//details-->
            </div>
            <!--//item-->
            <!--//item-->
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Backend Developer (internship)</h3>
                     <div class="time">May 2016–Oct 2016</div>
                  </div>
                  <!--//upper-row-->
                  <div class="company">Brāv Conflict Resolution, Michigan City, Indiana</div>
               </div>
               <!--//meta-->
               <div class="details">
                  <table>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Creating backend and REST API for online chat portal
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           Creating frontend for online chat portal
                        </td>
                     </tr>
                     <tr>
                        <td>
                           <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                           <i>Technologies: PHP, Javascript, JQuery, MySQL, WebRTC</i>
                        </td>
                     </tr>
                  </table>
                  <p><a href="assets/files/bravRec.pdf" target="_blank">Recommendation Letter</a> </p>
               </div>
               <!--//details-->
            </div>
            <!--//item-->
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">Student Assistant</h3>
                     <div class="time">Dec 2015–March 2016</div>
                  </div>
                  <!--//upper-row-->
                  <div class="company">University of Bonn, Bonn</div>
               </div>
               <!--//meta-->
               <div class="details">
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                              Development of custom scripts for spam prevention in MediaWiki system
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                              <i>Technologies: PHP, Javascript, JQuery, MySQL, MediaWiki</i>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>
         </section>
         <!--//freelance projects section-->
         <section class="section projects-section">
            <h2 class="section-title"><i class="fa fa-archive"></i>Freelance Projects</h2>

            <!--//intro-->
            <!--//item-->

            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">DiscoverDE <a href="https://e4sv.short.gy/de-qrcode" target="_blank"><i class="fa fa-external-link"></i></a></h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  React Native based German language preparation app
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Development of learning options - both listening and reading modes
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Multilingual (Russian / English)
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Development of interactive quizzes and tutorials
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>


            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">Java-1Z0-808 <a href="https://e4sv.short.gy/java-1z0808" target="_blank"><i class="fa fa-external-link"></i></a></h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  React Native based Java 1Z0-808 exam preparation app
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Choice to select quizzes randomly or by chapter
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>  Option to mark questions for further review
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Statistics tracking for correct and wrong answers
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>


            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">Bulls&Cows <a href="https://play.google.com/store/apps/details?id=com.device999.BullsAndCows" target="_blank"><i class="fa fa-external-link"></i></a></h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  React Native based code-breaking game
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>  2 Game modes: Competing against AI & Number finding challenge
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Game features statistics tracking for wins and losses
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Added badges which can be earned by winning within a predefined number of steps
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>


            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">ADHD</h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  React Native and Java based mobile app for ADHD sickness 
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>  Implemented the state management layer
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Created several endpoints for CRUD operations
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Utilized Docker to containerize the app
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>


            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">Graiger Scrapper</h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  Django-based Telegram bot designed to scrape data from automotive websites
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>  Developed the data scraping component
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Utilized the long polling mechanism to ensure real-time communication
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Integrated the bot with Django's ORM
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>


            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">My Cashier</h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  Development of a Java-based desktop middleware
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Implemented the connection and data transfer mechanism using the Bluetooth
                              protocol
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">Medicolonia</h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  Express.js based web-app of medical system
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Developed an intuitive and user-friendly interface
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i> Ensured that the user interface was compatible with a variety of devices
                           </td>
                        </tr>
                     </tbody>
                  </table>
                  <p><a href="assets/files/mediRec.pdf" target="_blank">Recommendation Letter</a> </p>
               </div>
               <!--//details-->
            </div>
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="project-title" style="color:#2d7788">Can Staging</h3>
                  </div>
                  <!--//upper-row-->
               </div>
               <!--//meta-->
               <div class="details">
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>
                              Express.js based web-app for detection of various cancer stages
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
               <!--//details-->
            </div>


            <!--//item-->
         </section>


         <!--//certifications section-->
         <section class="section projects-section">
            <h2 class="section-title"><i class="fa fa-book"></i>Publications</h2>
            <!--//intro-->
            <!--//item-->
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <div class="project-title" style="color:#2d7788">
                        <a target="_blank" href="http://ebooks.iospress.nl/volumearticle/51388">
                           Pay for Performance in Electronic Data Capture for Clinical Trials and Medical Registries
                        </a>
                     </div>
                  </div>
                  <!--//upper-row-->
               </div>
               <div class="details">
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <a target="_blank"
                                 href="http://ebooks.iospress.nl/volume/ict-for-health-science-research-proceedings-of-the-efmi-2019-special-topic-conference-7-10-april-2019-hanover-germany">
                                 ICT for Health Science Research</a>, EFMI 2019, IOS Press Ebooks
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
            </div>
         </section>

         <!--//publications section-->
         <section class="section projects-section">
            <h2 class="section-title"><i class="fa fa-bank"></i>Certifications</h2>
            <!--//intro-->
            <!--//item-->
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <div class="project-title" style="color:#2d7788">
                        <a target="_blank" href="https://catalog-education.oracle.com/pls/certview/sharebadge?id=4DFA7F09A2EAD10953BA53E9F64103429FB1A588287BFE288982670829A50AE0">
                           Oracle Certified Associate
                        </a>
                     </div>
                  </div>
                  <!--//upper-row-->
               </div>
               <div class="details">
                  <table>
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <a target="_blank"  href="assets/files//certificates/oracle_associate.pdf">
                                 Java SE 8 Programmer
                              </a>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
            </div>
         </section>
         <!--//courses section-->
         <section class="section experiences-section">
            <h2 class="section-title"><i class="fa fa-graduation-cap"></i>Courses</h2>
            
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">HackerRank</h3>
                  </div>
               </div>
               <div class="details">
                  <table style="width: 100%;">
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="./assets/files/certificates/udemy/Hackerank.pdf" target="_blank" style="margin-left:5px;">    
                                 Software Engineer Certification
                              </a>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
			   </div>
            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                    <h3 class="job-title">Udemy</h3>
                  </div>
               </div>
               <div class="details">
                  <table style="width: 100%;">
                     <thead>
                     </thead>
                     <tbody>

                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://www.udemy.com/course/spring-framework-5-beginner-to-guru" target="_blank" style="margin-left:5px;">    
                                 Spring Framework 5 (Coming Soon)
                              </a>
                           </td>
                        </tr>

                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://www.udemy.com/course/docker-mastery" target="_blank" style="margin-left:5px;">    
                                 Docker Mastery (Coming Soon)
                              </a>
                           </td>
                        </tr>

                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://www.udemy.com/certificate/UC-77c03c45-dcd7-41a6-8443-a60bcb4dbe70/" target="_blank" style="margin-left:5px;">    
                                 Multithreading and Concurrency
                              </a>
                           </td>
                        </tr>

                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-6af6defc-2cfc-4d0d-9eca-c0c0875a53ce/" target="_blank" style="margin-left:5px;">    
                                Functional Programming
                              </a>
                           </td>
                        </tr>

                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-67bd0537-6f9c-4b2f-a16b-c317ce8ffee9/" target="_blank" style="margin-left:5px;">    
                                Oracle Java Certification Course
                              </a>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-8096e2c0-c012-4bd1-b0d2-c4fc3117d07c/" target="_blank" style="margin-left:5px;">    
                                The Coding Interview Bootcamp in Java
                              </a>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-c04e91a8-3af9-45b9-a8da-ed0b2a8c3022/" target="_blank" style="margin-left:5px;">    
                                Algorithms and Data Structures in Java
                              </a>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-e9720113-8f2b-4fbc-9966-22a57cb6a513/" target="_blank" style="margin-left:5px;">    
                                 Java Programming: Step by Step from A to Z
                              </a>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-dd9316f8-9dbe-4e5b-92db-66542f1c1bc5/" style="margin-left:5px;" target="_blank" > 
                                   Starting with Quarkus
                              </a>
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>  
                              <a href="https://www.udemy.com/certificate/UC-f80aada9-87fe-43fd-8f1c-9c49f460f298/" target="_blank" style="margin-left:5px;"> 
                                 Relational Databases with Quarkus   
                              </a>                               
                           </td>
                        </tr>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://udemy.com/certificate/UC-1c4331cb-e833-42bb-9a66-8f379c4e603d/" target="_blank" style="margin-left:5px;">    
                                 Microservices with Quarkus
                              </a>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
			   </div>



            <div class="item">
               <div class="meta">
                  <div class="upper-row">
                     <h3 class="job-title">IBM (edX)</h3>
                  </div>
               </div>
               <div class="details">
                  <table style="width: 100%;">
                     <thead>
                     </thead>
                     <tbody>
                        <tr>
                           <td>
                              <i class="fa fa-dot-circle-o" aria-hidden="true" style="color:#2d7788"></i>                                 
                              <a href="https://courses.edx.org/certificates/f3826ebbbdc24fcf83b334998418b362" target="_blank" style="margin-left:5px;">    
                                 Python Basics for Data Science  
                              </a>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </div>
			   </div>
         </section>


         
      </div>
      <!--//main-body-->
   </div>
   <!-- Javascript -->
   
   <script type="text/javascript" src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
   <!-- custom js -->
   <script type="text/javascript" src="assets/js/main.js"></script>
</body>

</html>