<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page with <PERSON>que<PERSON> Banner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        header {
            background-color: #4CAF50;
            color: white;
            text-align: center;
            padding: 1rem;
        }

        main {
            flex: 1;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .content-section {
            margin-bottom: 2rem;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        h2 {
            color: #34495e;
            margin-bottom: 0.5rem;
        }

        p {
            margin-bottom: 1rem;
            text-align: justify;
        }

        .card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Marquee Banner Styles */
        .marquee-banner {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100vw;
            height: 50px;
            background-color: #030f09;
            overflow: hidden;
            display: flex;
            align-items: center;
            z-index: 1000;
        }

        .marquee-text {
            white-space: nowrap;
            color: white;
            font-weight: bold;
            font-size: 16px;
            animation: marqueeScroll 20s linear infinite;
            display: inline-block;
        }

        @keyframes marqueeScroll {
            0% {
                transform: translateX(100vw);
            }
            100% {
                transform: translateX(-100%);
            }
        }



        /* Add bottom margin to body to prevent content from being hidden behind marquee */
        body {
            margin-bottom: 50px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            main {
                padding: 1rem;
            }
            
            .marquee-text {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Welcome to Our Sample Website</h1>
        <p>A demonstration page with animated marquee banner</p>
    </header>

    <main>
        <section class="content-section">
            <h1>About Our Company</h1>
            <div class="card">
                <h2>Our Mission</h2>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            </div>
            
            <div class="card">
                <h2>Our Services</h2>
                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            </div>
        </section>

        <section class="content-section">
            <h1>Latest News</h1>
            <div class="card">
                <h2>Product Launch</h2>
                <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
            </div>
            
            <div class="card">
                <h2>Company Updates</h2>
                <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet.</p>
            </div>
        </section>

        <section class="content-section">
            <h1>Contact Information</h1>
            <div class="card">
                <h2>Get in Touch</h2>
                <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> (*************</p>
                <p><strong>Address:</strong> 123 Main Street, City, State 12345</p>
            </div>
        </section>
    </main>

    <!-- Marquee Banner -->
    <div class="marquee-banner">
        <div class="marquee-text">
            🎉 Welcome to our website! • Check out our latest products and services • Special offers available now
        </div>
    </div>
</body>
</html>
